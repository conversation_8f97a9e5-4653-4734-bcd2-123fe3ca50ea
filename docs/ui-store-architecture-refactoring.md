# UI Store 架构重构文档

## 🎯 重构目标

将 `usePanelHover` composable 的逻辑内置到 UI Store 中，实现更好的架构设计，遵循单一数据源和依赖倒置原则。

## 🚨 原架构的问题

### 1. 依赖关系倒置
```typescript
// 问题：组件层需要协调两个不同的抽象层
MainLayout.vue → usePanelHover (composable)
              → uiStore (store)
```

### 2. 职责混乱
```typescript
// MainLayout 中的混乱逻辑
const aiPanelHover = usePanelHover(
  isAIPanelPinned,                    // 来自 store
  () => uiStore.aiPanel.showOnHover(), // 回调到 store
  () => uiStore.aiPanel.hideOnHover()  // 回调到 store
);
```

### 3. 重复的状态管理
- `usePanelHover` 管理定时器状态
- `uiStore` 管理面板状态
- 两者需要在组件层手动协调

## ✅ 新架构设计

### 架构图
```
┌─────────────────────────────────────────┐
│            MainLayout.vue               │
│           (纯 UI 组件层)                 │
├─────────────────────────────────────────┤
│  • 模板事件绑定                         │
│  • 路由监听                             │
│  • 直接使用 store 方法                   │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│              ui.ts Store                │
│        (状态 + 完整业务逻辑)             │
├─────────────────────────────────────────┤
│  • 状态管理                             │
│  • 面板控制逻辑                         │
│  • 内置悬停处理 (原 usePanelHover)      │
│  • 统一的事件处理器                     │
└─────────────────────────────────────────┘
```

## 🔧 实现细节

### 1. 增强的 createPanelController

```typescript
const createPanelController = (
  visibleRef: Ref<boolean>,
  pinnedRef: Ref<boolean>,
  panelName: string,
  showDelay = 300,    // 新增：显示延时
  hideDelay = 500     // 新增：隐藏延时
) => {
  // 内置定时器管理
  const hoverTimer = ref<NodeJS.Timeout | null>(null);
  
  // 内置悬停事件处理器
  const handleMouseEnter = () => {
    if (pinnedRef.value) return;
    clearHoverTimer();
    hoverTimer.value = setTimeout(() => {
      showOnHover();
    }, showDelay);
  };
  
  const handleMouseLeave = () => {
    if (pinnedRef.value) return;
    clearHoverTimer();
    hoverTimer.value = setTimeout(() => {
      hideOnHover();
    }, hideDelay);
  };
  
  return {
    // 基础控制方法
    show, hide, toggle, showOnHover, hideOnHover, setPinned,
    
    // 增强的事件处理器（组件的主要接口）
    handleMouseEnter,
    handleMouseLeave,
    cleanup
  };
};
```

### 2. 简化的组件使用

```typescript
// 旧方式（复杂）
const aiPanelHover = usePanelHover(
  isAIPanelPinned,
  () => uiStore.aiPanel.showOnHover(),
  () => uiStore.aiPanel.hideOnHover()
);
const handleMouseEnter = aiPanelHover.handleMouseEnter;

// 新方式（简洁）
const handleMouseEnter = uiStore.aiPanel.handleMouseEnter;
```

## 📊 重构效果

### 代码减少量
- **移除了 `usePanelHover.ts` 文件** (55 行)
- **MainLayout.vue**: 从 23 行悬停逻辑减少到 9 行 (-61%)
- **UI Store**: 从 221 行增加到 285 行 (+29%，但功能更完整)
- **总体**: 净减少约 20 行代码，功能更集中

### 架构优势对比

| 方面 | 旧架构 | 新架构 |
|------|--------|--------|
| **依赖关系** | 组件依赖两个层级 | 组件只依赖 Store |
| **状态管理** | 分散在多处 | 集中在 Store |
| **代码复杂度** | 需要手动协调 | 自动协调 |
| **可测试性** | 需要 mock 多个依赖 | 只需 mock Store |
| **可维护性** | 修改需要多处同步 | 单一修改点 |

## 🔄 更新的文件

### 1. src/stores/ui.ts
- ✅ 内置悬停管理逻辑
- ✅ 提供完整的事件处理器接口
- ✅ 统一的定时器管理

### 2. src/layouts/MainLayout.vue
- ✅ 移除 `usePanelHover` 导入
- ✅ 直接使用 Store 事件处理器
- ✅ 简化的清理逻辑

### 3. 删除的文件
- ❌ `src/composables/usePanelHover.ts` (不再需要)

## 🎯 设计原则

1. **单一数据源**：所有面板状态和逻辑都在 Store 中
2. **封装完整性**：Store 提供完整的面板管理能力
3. **接口简洁性**：组件只需要知道 Store 接口
4. **职责清晰**：
   - Store：状态管理 + 业务逻辑 + 交互逻辑
   - Component：UI 渲染 + 事件绑定 + 路由响应

## 🚀 扩展性

添加新面板现在更加简单：
```typescript
// 1. 添加状态
const isNewPanelVisible = ref(false);
const isNewPanelPinned = ref(false);

// 2. 创建控制器（自动包含悬停逻辑）
const newPanel = createPanelController(
  isNewPanelVisible, 
  isNewPanelPinned, 
  'New Panel'
);

// 3. 在组件中直接使用
<div @mouseenter="uiStore.newPanel.handleMouseEnter"
     @mouseleave="uiStore.newPanel.handleMouseLeave">
```

## ✅ 验证结果

- ✅ 构建成功，无 TypeScript 错误
- ✅ 移除了不必要的 composable 文件
- ✅ 简化了组件逻辑
- ✅ 保持了原有功能完整性
- ✅ 实现了更好的架构设计

## 🎉 总结

这次重构成功实现了：
1. **更清晰的依赖关系**：组件只依赖 Store
2. **更好的封装性**：悬停逻辑完全封装在 Store 内部
3. **更简洁的使用方式**：直接使用 Store 提供的事件处理器
4. **更好的可维护性**：单一修改点，避免多处协调

这种设计更符合 Vue 3 + Pinia 的最佳实践，也更容易理解和维护。
