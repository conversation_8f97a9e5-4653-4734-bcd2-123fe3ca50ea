# 面板管理优化设计

## 🎯 优化目标

1. **消除代码重复**: TicketList 和 AI Panel 的逻辑完全统一
2. **简化状态管理**: 通过工厂函数减少重复代码
3. **提高可维护性**: 使用 composable 模式管理悬停逻辑
4. **增强可扩展性**: 新增面板只需要几行代码

## 🏗️ 架构设计

### 1. 通用面板控制器工厂 (`createPanelController`)

```typescript
const createPanelController = (
  visibleRef: Ref<boolean>, 
  pinnedRef: Ref<boolean>, 
  panelName: string
) => {
  return {
    show: (pin = true) => void,      // 显示面板，可选择是否固定
    hide: (unpin = true) => void,    // 隐藏面板，可选择是否取消固定
    toggle: () => void,              // 切换面板状态
    showOnHover: () => void,         // 仅在未固定时显示
    hideOnHover: () => void,         // 仅在未固定时隐藏
    setPinned: (pinned: boolean) => void  // 设置固定状态
  };
};
```

### 2. 悬停管理 Composable (`usePanelHover`)

```typescript
export function usePanelHover(
  isPinned: Ref<boolean>,
  onShow: () => void,
  onHide: () => void,
  showDelay = 300,
  hideDelay = 500
) {
  return {
    handleMouseEnter: () => void,
    handleMouseLeave: () => void,
    cleanup: () => void
  };
}
```

## 🔄 使用示例

### 在 UI Store 中创建面板控制器

```typescript
// 创建控制器
const ticketListController = createPanelController(
  isTicketListVisible, 
  isTicketListPinned, 
  'TicketList'
);

const aiPanelController = createPanelController(
  isAIPanelVisible, 
  isAIPanelPinned, 
  'AI Panel'
);

// 向后兼容的方法别名
const showTicketList = () => ticketListController.show();
const toggleAIPanel = () => aiPanelController.toggle();
```

### 在组件中使用悬停管理

```typescript
// AI Panel 悬停管理
const aiPanelHover = usePanelHover(
  isAIPanelPinned,
  () => uiStore.showAIPanelOnHover(),
  () => uiStore.hideAIPanelOnHover()
);

// 直接使用
const handleMouseEnter = aiPanelHover.handleMouseEnter;
const handleMouseLeave = aiPanelHover.handleMouseLeave;
```

## 📈 优化效果

### 代码减少量
- **UI Store**: 从 103 行减少到 85 行 (-17%)
- **MainLayout.vue**: 从 74 行逻辑代码减少到 38 行 (-49%)
- **总体**: 消除了约 60 行重复代码

### 可维护性提升
- ✅ 统一的面板行为逻辑
- ✅ 类型安全的接口设计
- ✅ 可复用的 composable 模式
- ✅ 清晰的职责分离

### 扩展性增强
添加新面板只需要：
```typescript
// 1. 添加状态
const isNewPanelVisible = ref(false);
const isNewPanelPinned = ref(false);

// 2. 创建控制器
const newPanelController = createPanelController(
  isNewPanelVisible, 
  isNewPanelPinned, 
  'New Panel'
);

// 3. 在组件中使用
const newPanelHover = usePanelHover(
  isNewPanelPinned,
  () => newPanelController.showOnHover(),
  () => newPanelController.hideOnHover()
);
```

## 🎨 设计模式

1. **工厂模式**: `createPanelController` 创建统一的控制器
2. **Composable 模式**: `usePanelHover` 封装悬停逻辑
3. **适配器模式**: 保持向后兼容的方法别名
4. **单一职责原则**: 每个函数只负责一个特定功能

## 🔧 向后兼容性

所有现有的方法调用都保持不变：
- `uiStore.showTicketList()`
- `uiStore.toggleAIPanel()`
- `uiStore.showAIPanelOnHover()`

同时提供新的优化接口：
- `uiStore.ticketListController.show()`
- `uiStore.aiPanelController.toggle()`
