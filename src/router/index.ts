import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: MainLayout,
      children: [
        {
          path: '',
          redirect: '/tickets/created'  // 改为默认跳转到我创建的工单
        },
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'tickets/:filter',
          name: 'tickets',
          component: () => import('@/views/TicketView.vue')
        },
        {
          path: 'ticket/:id',
          name: 'ticket-detail',
          component: () => import('@/views/TicketDetailView.vue')
        },
        {
          path: 'create',
          name: 'create-ticket',
          component: () => import('@/views/CreateTicketView.vue')
        }
      ]
    }
  ]
});

export default router;