import { defineStore } from 'pinia';
import { ref, type Ref } from 'vue';

export type Theme = 'light' | 'dark';
export type Language = 'zh' | 'en';
export type Priority = 'Minor' | 'Low' | 'Medium' | 'High' | 'Critical';

// Memory settings for ticket creation
export interface TicketCreationDefaults {
  priority: Priority;
  component: string;
  service_package: string;
}

export const useUIStore = defineStore('ui', () => {
  // TicketList panel state - default visible and pinned
  const isTicketListVisible = ref(true);
  const isTicketListPinned = ref(true);

  // AI Panel state - default hidden
  const isAIPanelVisible = ref(false);
  const isAIPanelPinned = ref(false);

  // Theme setting - default to light
  const theme = ref<Theme>('light');

  // Language setting - default to Chinese
  const language = ref<Language>('zh');

  // Ticket creation defaults - remember settings from last successful ticket creation
  const ticketCreationDefaults = ref<TicketCreationDefaults>({
    priority: 'Medium',
    component: '',
    service_package: ''
  });

  // Create panel controller with built-in hover management
  const createPanelController = (
    visibleRef: Ref<boolean>,
    pinnedRef: Ref<boolean>,
    panelName: string,
    showDelay = 300,
    hideDelay = 500
  ) => {
    // Internal hover timer management
    const hoverTimer = ref<NodeJS.Timeout | null>(null);

    const clearHoverTimer = () => {
      if (hoverTimer.value) {
        clearTimeout(hoverTimer.value);
        hoverTimer.value = null;
      }
    };

    const show = (pin = true) => {
      clearHoverTimer();
      visibleRef.value = true;
      if (pin) {
        pinnedRef.value = true;
      }
      if (import.meta.env.DEV) {
        console.log(`${panelName} shown${pin ? ' and pinned' : ''}`);
      }
    };

    const hide = (unpin = true) => {
      clearHoverTimer();
      visibleRef.value = false;
      if (unpin) {
        pinnedRef.value = false;
      }
      if (import.meta.env.DEV) {
        console.log(`${panelName} hidden${unpin ? ' and unpinned' : ''}`);
      }
    };

    const toggle = () => {
      if (visibleRef.value) {
        hide(true);
      } else {
        show(true);
      }
      if (import.meta.env.DEV) {
        console.log(`${panelName} toggled:`, visibleRef.value ? 'visible & pinned' : 'hidden & unpinned');
      }
    };

    const showOnHover = () => {
      if (!pinnedRef.value) {
        visibleRef.value = true;
      }
    };

    const hideOnHover = () => {
      if (!pinnedRef.value) {
        visibleRef.value = false;
      }
    };

    const setPinned = (pinned: boolean) => {
      pinnedRef.value = pinned;
      if (import.meta.env.DEV) {
        console.log(`${panelName} pinned state changed:`, pinned);
      }
    };

    // Enhanced hover event handlers with built-in delay and state checking
    const handleMouseEnter = () => {
      // If already pinned, don't respond to hover
      if (pinnedRef.value) return;

      clearHoverTimer();
      hoverTimer.value = setTimeout(() => {
        showOnHover();
        if (import.meta.env.DEV) {
          console.log(`${panelName} shown on hover`);
        }
      }, showDelay);
    };

    const handleMouseLeave = () => {
      // If already pinned, don't respond to hover
      if (pinnedRef.value) return;

      clearHoverTimer();
      hoverTimer.value = setTimeout(() => {
        hideOnHover();
        if (import.meta.env.DEV) {
          console.log(`${panelName} hidden on hover leave`);
        }
      }, hideDelay);
    };

    // Cleanup function for component unmounting
    const cleanup = () => {
      clearHoverTimer();
    };

    return {
      // Basic panel controls
      show,
      hide,
      toggle,
      showOnHover,
      hideOnHover,
      setPinned,

      // Enhanced event handlers (main interface for components)
      handleMouseEnter,
      handleMouseLeave,
      cleanup
    };
  };

  // Panel controllers
  const ticketList = createPanelController(
    isTicketListVisible,
    isTicketListPinned,
    'TicketList'
  );

  const aiPanel = createPanelController(
    isAIPanelVisible,
    isAIPanelPinned,
    'AI Panel'
  );

  // Theme control functions
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light';
    applyTheme();
    if (import.meta.env.DEV) {
      console.log('Theme switched to:', theme.value);
    }
  };

  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme;
    applyTheme();
    if (import.meta.env.DEV) {
      console.log('Theme set to:', newTheme);
    }
  };

  // Apply theme to page
  const applyTheme = () => {
    const html = document.documentElement;
    if (theme.value === 'dark') {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }
  };

  // Language control functions
  const setLanguage = (newLanguage: Language) => {
    language.value = newLanguage;
    if (import.meta.env.DEV) {
      console.log('Language set to:', newLanguage);
    }
    // TODO: Implement actual language switching logic
  };

  // Ticket creation defaults management
  const updateTicketCreationDefaults = (defaults: Partial<TicketCreationDefaults>) => {
    ticketCreationDefaults.value = {
      ...ticketCreationDefaults.value,
      ...defaults
    };
    if (import.meta.env.DEV) {
      console.log('Ticket creation defaults updated:', ticketCreationDefaults.value);
    }
  };

  const getTicketCreationDefaults = () => {
    return { ...ticketCreationDefaults.value };
  };

  const resetTicketCreationDefaults = () => {
    ticketCreationDefaults.value = {
      priority: 'Medium',
      component: '',
      service_package: ''
    };
    if (import.meta.env.DEV) {
      console.log('Ticket creation defaults reset');
    }
  };

  // Initialize settings (apply theme)
  const initializeSettings = () => {
    applyTheme();
    if (import.meta.env.DEV) {
      console.log('Settings initialized:', {
        theme: theme.value,
        language: language.value,
        ticketDefaults: ticketCreationDefaults.value
      });
    }
  };

  return {
    // Panel states (read-only)
    isTicketListVisible,
    isTicketListPinned,
    isAIPanelVisible,
    isAIPanelPinned,

    // Panel controllers (main interface)
    ticketList,
    aiPanel,

    // Theme and language
    theme,
    language,
    toggleTheme,
    setTheme,
    setLanguage,
    initializeSettings,

    // Ticket creation defaults
    ticketCreationDefaults,
    updateTicketCreationDefaults,
    getTicketCreationDefaults,
    resetTicketCreationDefaults
  };
}, {
  persist: {
    key: 'iticket-ui-store',
    storage: localStorage,
    paths: ['theme', 'language', 'ticketCreationDefaults']
  }
});