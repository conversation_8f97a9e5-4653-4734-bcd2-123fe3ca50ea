import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Ticket } from '@/types/ticket';
import { useUserStore } from '@/stores/user';
import { ticketAPI } from '@/api/ticket';

export const useTicketStore = defineStore('ticket', () => {
  // 统一的票据数据存储 - 所有票据都存储在这里
  const allTickets = ref<Ticket[]>([]);
  const currentTicket = ref<Ticket | null>(null);
  const loading = ref(false);
  const currentFilter = ref<'created' | 'assigned' | null>(null);

  // 获取用户存储实例
  const userStore = useUserStore();

  // 计算属性：根据当前用户邮箱过滤创建的票据，按updated时间倒序排列
  const createdTickets = computed(() => {
    const userEmail = userStore.getCurrentUserEmail();
    return allTickets.value
      .filter(ticket => ticket.reporter_email === userEmail)
      .sort((a, b) => new Date(b.updated).getTime() - new Date(a.updated).getTime());
  });

  // 计算属性：根据当前用户邮箱过滤分配的票据，按updated时间倒序排列
  const assignedTickets = computed(() => {
    const userEmail = userStore.getCurrentUserEmail();
    return allTickets.value
      .filter(ticket => ticket.assignee_email === userEmail)
      .sort((a, b) => new Date(b.updated).getTime() - new Date(a.updated).getTime());
  });

  // 获取当前视图的票据（根据过滤条件）
  const getCurrentTickets = () => {
    return currentFilter.value === 'created' ? createdTickets.value : assignedTickets.value;
  };

  // 加载所有票据（一次性加载，不再区分created/assigned）
  const loadTickets = async (forceRefresh: boolean = false) => {
    try {
      loading.value = true;

      // 如果强制刷新，清空现有数据
      if (forceRefresh) {
        allTickets.value = [];
      }

      // 如果没有数据或强制刷新，从API加载
      if (allTickets.value.length === 0 || forceRefresh) {
        const userEmail = userStore.getCurrentUserEmail();
        await loadTicketsFromAPI(userEmail);
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error loading tickets:', error);
      }
    } finally {
      loading.value = false;
    }
  };

  // Get ticket with specified ID from all tickets
  const getTicketById = (id: string): Ticket | null => {
    return allTickets.value.find((ticket: Ticket) => ticket.issues_id === id) || null;
  };

  // Set current ticket (从所有票据中查找)
  const setCurrentTicket = async (id: string) => {
    try {
      // 首先在现有的统一存储中查找
      let ticket = getTicketById(id);

      if (!ticket) {
        // 如果没找到，从API获取完整数据并搜索
        loading.value = true;
        const userEmail = userStore.getCurrentUserEmail();
        await loadTicketsFromAPI(userEmail);

        // 再次在更新后的存储中查找
        ticket = getTicketById(id);
      }

      if (ticket) {
        currentTicket.value = ticket;
      } else {
        currentTicket.value = null;
        if (import.meta.env.DEV) {
          console.error('Ticket not found:', id);
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error setting current ticket:', error);
      }
      currentTicket.value = null;
    } finally {
      loading.value = false;
    }
  };

  const createTicket = async (ticketData: Partial<Ticket>) => {
    loading.value = true;
    try {
      const userEmail = userStore.getCurrentUserEmail();

      // 设置创建者邮箱
      const ticketToCreate = {
        ...ticketData,
        reporter_email: userEmail
      };

      // 调用票据API创建票据
      const newTicket = await ticketAPI.createTicket(ticketToCreate);

      // 确保在创建工单后，如果当前没有加载"我创建的工单"列表，则加载它
      if (currentFilter.value !== 'created') {
        currentFilter.value = 'created';
        // 如果没有数据，则先加载初始数据
        if (createdTickets.value.length === 0) {
          await loadTicketsFromAPI(userEmail);
        }
      }

      // 将新工单添加到统一存储的顶部
      allTickets.value.unshift(newTicket);

      return newTicket;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error creating ticket:', error);
      }
      throw error;
    } finally {
      loading.value = false;
    }
  };



  // 内部方法：从API加载票据数据
  const loadTicketsFromAPI = async (userEmail: string) => {
    try {
      // 调用票据API获取该用户相关的所有票据
      const userTickets = await ticketAPI.getTicketsByUser(userEmail);

      // 更新统一存储，避免重复
      const existingIds = new Set(allTickets.value.map(t => t.issues_id));
      const newTickets = userTickets.filter(t => !existingIds.has(t.issues_id));
      allTickets.value.push(...newTickets);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error loading tickets from API:', error);
      }
      throw error;
    }
  };





  // 清除本地缓存
  const clearCache = () => {
    // 在开发环境或使用 mock 数据时都允许清除缓存
    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      allTickets.value = [];
      currentTicket.value = null;
      currentFilter.value = null;
      console.log('Local cache cleared');
    }
  };

  // 辅助方法：计算票据总工时
  const getTotalWorkTime = (ticket: Ticket): number => {
    if (!ticket.worklogs) return 0;
    return ticket.worklogs.reduce((total, worklog) =>
      total + (worklog.worklog_timeSpent || 0), 0);
  };

  // 辅助方法：获取票据最新的工作日志
  const getLatestWorklog = (ticket: Ticket) => {
    if (!ticket.worklogs || ticket.worklogs.length === 0) return null;
    return ticket.worklogs.reduce((latest, current) => {
      const latestTime = new Date(latest.worklog_time || 0).getTime();
      const currentTime = new Date(current.worklog_time || 0).getTime();
      return currentTime > latestTime ? current : latest;
    });
  };

  // 辅助方法：按时间排序工作日志
  const getSortedWorklogs = (ticket: Ticket, ascending: boolean = false) => {
    if (!ticket.worklogs) return [];
    return [...ticket.worklogs].sort((a, b) => {
      const timeA = new Date(a.worklog_time || 0).getTime();
      const timeB = new Date(b.worklog_time || 0).getTime();
      return ascending ? timeA - timeB : timeB - timeA;
    });
  };

  // 更新store中的特定票据
  const updateTicketInStore = (updatedTicket: Ticket) => {
    const index = allTickets.value.findIndex(t => t.issues_id === updatedTicket.issues_id);
    if (index !== -1) {
      allTickets.value[index] = updatedTicket;

      // 如果更新的是当前票据，也更新currentTicket
      if (currentTicket.value?.issues_id === updatedTicket.issues_id) {
        currentTicket.value = updatedTicket;
      }
    }
  };

  return {
    // 数据
    allTickets,
    createdTickets,
    assignedTickets,
    currentTicket,
    loading,
    currentFilter,
    getCurrentTickets,

    // 方法
    loadTickets,
    getTicketById,
    setCurrentTicket,
    createTicket,
    clearCache,

    // 辅助方法
    getTotalWorkTime,
    getLatestWorklog,
    getSortedWorklogs,
    updateTicketInStore
  };
}, {
  persist: {
    key: 'iticket-store',
    storage: localStorage,
    paths: ['allTickets', 'dataVersion'] // 持久化票据存储和数据版本
  }
});
