<template>
  <n-config-provider :theme-overrides="themeOverrides" :theme="naiveTheme">
    <n-message-provider>
      <n-dialog-provider>
        <router-view />
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NConfigProvider, NMessageProvider, NDialogProvider, darkTheme } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { useUIStore } from '@/stores/ui';
import { getNaiveUIThemeOverrides } from '@/utils/colors';

const uiStore = useUIStore();
const { theme } = storeToRefs(uiStore);

// 根据主题设置选择 Naive UI 主题
const naiveTheme = computed(() => {
  return theme.value === 'dark' ? darkTheme : null;
});

// 使用统一的颜色配置
const themeOverrides = computed(() => {
  return getNaiveUIThemeOverrides(theme.value === 'dark');
});
</script>

<style>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 应用程序特定的全局样式 */
html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

#app {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: background-color 0.3s ease;
}

/* 深色主题下的滚动条样式 - 使用与 Tailwind 配置一致的颜色 */
html.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

html.dark ::-webkit-scrollbar-track {
  background: rgb(55 65 81); /* gray-700 / dark-surface */
}

html.dark ::-webkit-scrollbar-thumb {
  background: rgb(107 114 128); /* gray-500 / dark-border-light */
  border-radius: 4px;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175); /* gray-400 / dark-text-disabled */
}
</style>