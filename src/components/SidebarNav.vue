<template>
  <div class="h-full flex flex-col py-4">
    <!-- Navigation Items -->
    <div class="flex-1 space-y-3 px-2">
      <!-- Dashboard Button - 放在最上面 -->
      <n-tooltip placement="right">
        <template #trigger>
          <div
            @click="navigateTo(ROUTE_CONFIG.DASHBOARD.path)"
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200',
              isActive(ROUTE_CONFIG.DASHBOARD.path)
                ? 'bg-blue-800 text-white shadow-lg'
                : 'text-blue-100 hover:bg-blue-700 hover:text-white'
            ]"
          >
            <n-icon size="20">
              <component :is="ROUTE_CONFIG.DASHBOARD.icon" />
            </n-icon>
          </div>
        </template>
        {{ ROUTE_CONFIG.DASHBOARD.label }}
      </n-tooltip>

      <!-- 分隔线 -->
      <div class="w-8 h-px bg-blue-500 mx-auto my-2"></div>

      <n-tooltip placement="right" v-for="item in navItems" :key="item.path">
        <template #trigger>
          <div
            @click="navigateTo(item.path)"
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200',
              isActive(item.path) 
                ? 'bg-blue-800 text-white shadow-lg' 
                : 'text-blue-100 hover:bg-blue-700 hover:text-white'
            ]"
          >
            <n-icon size="20">
              <component :is="item.icon" />
            </n-icon>
          </div>
        </template>
        {{ item.label }}
      </n-tooltip>

      <!-- Create Ticket Button -->
      <n-tooltip placement="right">
        <template #trigger>
          <div
            @click="navigateTo(ROUTE_CONFIG.CREATE_TICKET.path)"
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200 shadow-lg',
              isActive(ROUTE_CONFIG.CREATE_TICKET.path)
                ? 'bg-blue-800 text-white'
                : 'text-blue-100 hover:bg-blue-700 hover:text-white'
            ]"
          >
            <n-icon size="20">
              <component :is="ROUTE_CONFIG.CREATE_TICKET.icon" />
            </n-icon>
          </div>
        </template>
        {{ ROUTE_CONFIG.CREATE_TICKET.label }}
      </n-tooltip>
    </div>

    <!-- Account and Settings Buttons at bottom -->
    <div class="px-2 mb-4 space-y-3">
      <!-- Account Button -->
      <n-dropdown
        :options="accountMenuOptions"
        @select="handleAccountMenuSelect"
        trigger="click"
        placement="right-end"
        :show-arrow="true"
      >
        <n-tooltip placement="right">
          <template #trigger>
            <div
              class="w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer text-blue-100 hover:bg-blue-700 hover:text-white transition-all duration-200"
            >
              <n-icon size="20">
                <PersonCircleOutline />
              </n-icon>
            </div>
          </template>
          {{ userInfo.email }}
        </n-tooltip>
      </n-dropdown>

      <!-- Settings Button -->
      <n-dropdown
        :options="settingsMenuOptions"
        @select="handleSettingsMenuSelect"
        trigger="click"
        placement="right-end"
        :show-arrow="true"
      >
        <n-tooltip placement="right">
          <template #trigger>
            <div
              class="w-12 h-12 rounded-lg flex items-center justify-center cursor-pointer text-blue-100 hover:bg-blue-700 hover:text-white transition-all duration-200"
            >
              <n-icon size="20">
                <SettingsOutline />
              </n-icon>
            </div>
          </template>
          Settings
        </n-tooltip>
      </n-dropdown>
    </div>
  </div>

  <!-- Switch User Modal -->
  <n-modal
    v-model:show="showSwitchUserModal"
    preset="dialog"
    title="Switch User"
    positive-text="Switch"
    negative-text="Cancel"
    @positive-click="handleSwitchUser"
    @negative-click="showSwitchUserModal = false"
  >
    <n-space vertical>
      <div>Enter the email address of the user you want to switch to:</div>
      <n-input
        v-model:value="newUserEmail"
        placeholder="<EMAIL>"
        @keyup.enter="handleSwitchUser"
      />
    </n-space>
  </n-modal>

  <!-- Config Modal -->
  <ConfigModal v-model:show="showConfigModal" />
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { NIcon, NTooltip, NDropdown, NModal, NInput, NSpace, useMessage } from 'naive-ui';
import {
  AddOutline,
  SettingsOutline,
  StatsChartOutline,
  SunnyOutline,
  MoonOutline,
  LanguageOutline,
  CheckmarkOutline,
  RefreshOutline,
  PersonCircleOutline,
  SwapHorizontalOutline,
  LogInOutline,
  LogOutOutline
} from '@vicons/ionicons5';
import { storeToRefs } from 'pinia';
import { useTicketStore } from '@/stores/ticket';
import { useUIStore } from '@/stores/ui';
import { useUserStore } from '@/stores/user';
import ConfigModal from '@/components/ConfigModal.vue';

const router = useRouter();
const route = useRoute();
const ticketStore = useTicketStore();
const uiStore = useUIStore();
const userStore = useUserStore();
const message = useMessage();

const { theme, language } = storeToRefs(uiStore);
const { userInfo } = storeToRefs(userStore);

// Switch user modal state
const showSwitchUserModal = ref(false);
const newUserEmail = ref('');

// Config modal state
const showConfigModal = ref(false);

// 路由配置类型定义
interface RouteConfig {
  path: string;
  label: string;
  icon: any;
  panelAction: 'hide' | 'show' | 'none';
  ticketType?: string; // 可选的工单类型，用于工单详情页的导航高亮
}

// 路由配置对象 - 集中管理所有导航相关的配置
const ROUTE_CONFIG: Record<string, RouteConfig> = {
  DASHBOARD: {
    path: '/dashboard',
    label: 'Dashboard',
    icon: StatsChartOutline,
    panelAction: 'hide' // 面板行为：hide, show, none
  },
  CREATE_TICKET: {
    path: '/create',
    label: 'Create Ticket',
    icon: AddOutline,
    panelAction: 'hide'
  },
  TICKETS_CREATED: {
    path: '/tickets/created',
    label: 'Created by Me',
    icon: LogOutOutline,
    panelAction: 'show',
    ticketType: 'created' // 用于工单类型判断
  },
  TICKETS_ASSIGNED: {
    path: '/tickets/assigned',
    label: 'Assigned to Me',
    icon: LogInOutline,
    panelAction: 'show',
    ticketType: 'assigned'
  }
};

// 从配置对象生成导航项
const navItems = [
  ROUTE_CONFIG.TICKETS_CREATED,
  ROUTE_CONFIG.TICKETS_ASSIGNED
];

// Account 菜单选项
const accountMenuOptions = computed(() => [
  {
    type: 'group',
    label: 'Account',
    key: 'account-group',
    children: [
      {
        label: 'Switch User',
        key: 'switch-user',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(SwapHorizontalOutline) })
      }
    ]
  }
]);

// 设置菜单选项
const settingsMenuOptions = computed(() => [
  {
    type: 'group',
    label: 'Theme',
    key: 'theme-group',
    children: [
      {
        label: 'Light Theme',
        key: 'theme-light',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(SunnyOutline) }),
        suffix: theme.value === 'light' ? () => h(NIcon, { size: 16, color: '#10b981' }, { default: () => h(CheckmarkOutline) }) : undefined
      },
      {
        label: 'Dark Theme',
        key: 'theme-dark',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(MoonOutline) }),
        suffix: theme.value === 'dark' ? () => h(NIcon, { size: 16, color: '#10b981' }, { default: () => h(CheckmarkOutline) }) : undefined
      }
    ]
  },
  {
    type: 'divider'
  },
  {
    type: 'group',
    label: 'Language',
    key: 'language-group',
    children: [
      {
        label: 'English',
        key: 'language-en',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(LanguageOutline) }),
        suffix: language.value === 'en' ? () => h(NIcon, { size: 16, color: '#10b981' }, { default: () => h(CheckmarkOutline) }) : undefined
      },
      {
        label: '中文',
        key: 'language-zh',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(LanguageOutline) }),
        suffix: language.value === 'zh' ? () => h(NIcon, { size: 16, color: '#10b981' }, { default: () => h(CheckmarkOutline) }) : undefined
      }
    ]
  },
  {
    type: 'divider'
  },
  {
    type: 'group',
    label: 'Configuration',
    key: 'config-group',
    children: [
      {
        label: 'Configs',
        key: 'configs',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(SettingsOutline) })
      }
    ]
  },
  {
    type: 'divider'
  },
  {
    type: 'group',
    label: 'Dev Tools',
    key: 'dev-tools-group',
    children: [
      {
        label: 'Reset Mock Data',
        key: 'reset-mock-data',
        icon: () => h(NIcon, { size: 16 }, { default: () => h(RefreshOutline) })
      }
    ]
  }
]);

// 处理 Account 菜单选择
const handleAccountMenuSelect = (key: string) => {
  switch (key) {
    case 'switch-user':
      showSwitchUserModal.value = true;
      newUserEmail.value = userInfo.value.email;
      break;
  }
};

// 处理设置菜单选择
const handleSettingsMenuSelect = (key: string) => {
  switch (key) {
    case 'theme-light':
      uiStore.setTheme('light');
      message.success('Switched to light theme');
      break;
    case 'theme-dark':
      uiStore.setTheme('dark');
      message.success('Switched to dark theme');
      break;
    case 'language-zh':
      uiStore.setLanguage('zh');
      message.info('Chinese language feature under development');
      break;
    case 'language-en':
      uiStore.setLanguage('en');
      message.success('Switched to English');
      break;
    case 'configs':
      showConfigModal.value = true;
      break;
    case 'reset-mock-data':
      handleResetMockData();
      break;
  }
};

// 处理用户切换
const handleSwitchUser = async () => {
  if (!newUserEmail.value.trim()) {
    message.error('Please enter a valid email address');
    return;
  }

  try {
    // 更新用户信息
    userStore.setCurrentUserEmail(newUserEmail.value.trim());

    // 强制刷新票据数据，加载新用户的数据
    await ticketStore.loadTickets(true);

    // 关闭模态框
    showSwitchUserModal.value = false;

    // 显示成功消息
    message.success(`Switched to user: ${newUserEmail.value.trim()}`);

    // 导航到 dashboard 以刷新数据
    router.push('/dashboard');

    if (import.meta.env.DEV) {
      console.log('User switched to:', newUserEmail.value.trim());
    }
  } catch (error) {
    message.error('Error occurred while switching user');
    if (import.meta.env.DEV) {
      console.error('Error switching user:', error);
    }
  }
};

// 处理重置 Mock 数据
const handleResetMockData = () => {
  try {
    // 清除本地缓存，下次访问时会重新加载
    ticketStore.clearCache();

    // 显示成功消息
    message.success('Local cache cleared, data will be reloaded');

    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      console.log('Mock data reset completed');
    }
  } catch (error) {
    message.error('Error occurred while resetting data');
    if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
      console.error('Error resetting mock data:', error);
    }
  }
};

// 根据路径获取路由配置
const getRouteConfig = (path: string) => {
  return Object.values(ROUTE_CONFIG).find(config => config.path === path);
};

const navigateTo = (path: string) => {
  const routeConfig = getRouteConfig(path);

  if (routeConfig) {
    // 根据配置的面板行为控制 TicketList 显示状态
    switch (routeConfig.panelAction) {
      case 'hide':
        uiStore.ticketList.hide();
        if (import.meta.env.DEV) {
          console.log(`Navigating to ${routeConfig.label} - hiding TicketList`);
        }
        break;
      case 'show':
        uiStore.ticketList.show();
        uiStore.ticketList.setPinned(true);
        if (import.meta.env.DEV) {
          console.log(`Navigating to ${routeConfig.label} - showing and pinning TicketList`);
        }
        break;
      // 'none' 或其他情况不做任何操作
    }
  }

  router.push(path);
};

const isActive = (path: string) => {
  const routeConfig = getRouteConfig(path);

  if (!routeConfig) {
    return false;
  }

  // 直接路径匹配
  if (route.path === path) {
    return true;
  }

  // 对于工单列表页面，检查工单详情页是否属于该类型
  if (routeConfig.ticketType && route.name === 'ticket-detail') {
    const currentTicket = ticketStore.currentTicket;
    if (currentTicket) {
      // 正确的判断方式：检查当前票据是否在对应的票据列表中
      if (routeConfig.ticketType === 'created') {
        return ticketStore.createdTickets.some(ticket => ticket.issues_id === currentTicket.issues_id);
      } else if (routeConfig.ticketType === 'assigned') {
        return ticketStore.assignedTickets.some(ticket => ticket.issues_id === currentTicket.issues_id);
      }
    }
  }

  return false;
};
</script>

