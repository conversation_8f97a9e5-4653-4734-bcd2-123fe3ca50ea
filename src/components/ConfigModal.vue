<template>
  <n-modal
    v-model:show="showModal"
    preset="dialog"
    title="系统配置"
    :mask-closable="false"
    style="width: 500px"
  >
    <template #header>
      <div class="flex items-center space-x-2">
        <n-icon size="20">
          <SettingsOutline />
        </n-icon>
        <span>系统配置</span>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Ticket History Days -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          票据历史天数
        </label>
        <n-input-number
          v-model:value="formData.ticket_history_days"
          :min="1"
          :max="365"
          :step="1"
          placeholder="请输入天数"
          class="w-full"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
          从API服务器抓取票据的历史天数（1-365天）
        </p>
      </div>

      <!-- Ticket Refresh From -->
      <div class="space-y-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          最近更新时间
        </label>
        <n-date-picker
          v-model:value="refreshFromTimestamp"
          type="datetime"
          clearable
          placeholder="选择最近更新时间"
          class="w-full"
          :time-picker-props="{ format: 'HH:mm:ss' }"
          format="yyyy-MM-dd HH:mm:ss"
        />
        <p class="text-xs text-gray-500 dark:text-gray-400">
          前端最近一次从后端更新票据的时间（留空表示使用历史天数计算）
        </p>
      </div>
    </div>

    <template #action>
      <div class="flex justify-end space-x-3">
        <n-button @click="handleCancel">
          取消
        </n-button>
        <n-button
          type="primary"
          @click="handleSave"
          :loading="saving"
        >
          保存
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { 
  NModal, 
  NButton, 
  NInputNumber, 
  NDatePicker,
  NIcon,
  useMessage 
} from 'naive-ui';
import { SettingsOutline } from '@vicons/ionicons5';
import { useUIStore } from '@/stores/ui';

// Props
interface Props {
  show: boolean;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean];
}>();

// Store and message
const uiStore = useUIStore();
const message = useMessage();

// Modal state
const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const saving = ref(false);

// Form data
const formData = reactive({
  ticket_history_days: 60,
  ticket_refresh_from: null as string | null
});

// Date picker timestamp (for easier handling)
const refreshFromTimestamp = ref<number | null>(null);

// Watch timestamp changes and convert to ISO string
watch(refreshFromTimestamp, (newValue) => {
  if (newValue) {
    formData.ticket_refresh_from = new Date(newValue).toISOString();
  } else {
    formData.ticket_refresh_from = null;
  }
});

// Initialize form data when modal opens
watch(() => props.show, (show) => {
  if (show) {
    const config = uiStore.getTicketConfig();
    formData.ticket_history_days = config.ticket_history_days;
    formData.ticket_refresh_from = config.ticket_refresh_from;
    
    // Convert ISO string to timestamp for date picker
    if (config.ticket_refresh_from) {
      refreshFromTimestamp.value = new Date(config.ticket_refresh_from).getTime();
    } else {
      refreshFromTimestamp.value = null;
    }
  }
});

// Handle cancel
const handleCancel = () => {
  showModal.value = false;
};

// Handle save
const handleSave = async () => {
  try {
    saving.value = true;
    
    // Validate form data
    if (formData.ticket_history_days < 1 || formData.ticket_history_days > 365) {
      message.error('票据历史天数必须在1-365天之间');
      return;
    }
    
    // Update UI store
    uiStore.updateTicketConfig({
      ticket_history_days: formData.ticket_history_days,
      ticket_refresh_from: formData.ticket_refresh_from
    });
    
    message.success('配置保存成功');
    showModal.value = false;
  } catch (error) {
    console.error('Error saving config:', error);
    message.error('配置保存失败');
  } finally {
    saving.value = false;
  }
};
</script>
