import request from '@/utils/request';
import type { Ticket, Worklog } from '@/types/ticket';

/**
 * 真实API响应接口
 */
export interface APIResponse<T> {
  status: string;
  code: number;
  message: string;
  opId: string | null;
  data: T;
}

/**
 * 票据API接口定义
 */
export interface TicketAPI {
  getTicketsByUser(userEmail: string, from: string): Promise<Ticket[]>;
  createTicket(ticketData: Partial<Ticket>): Promise<Ticket>;
  updateTicket(ticketId: string, updates: Partial<Ticket>): Promise<Ticket>;
  getTicketById(ticketId: string): Promise<Ticket>;
  deleteTicket(ticketId: string): Promise<void>;
}

/**
 * 票据API实现类
 */
class TicketAPIImpl implements TicketAPI {
  /**
   * 根据用户邮箱获取票据
   * 真实API: GET https://ci.labtools.china.nsn-net.net/api/tickets/?userEmail=xxx&from=xxx
   */
  async getTicketsByUser(userEmail: string, from: string): Promise<Ticket[]> {
    try {

      // 构建查询参数
      const params = {
        userEmail: userEmail,
        from: from
      };

      if (import.meta.env.DEV) {
        console.log('Request params:', params);
      }

      // 使用 request 工具调用API
      const response = await request.get<APIResponse<Ticket[]>>('/tickets', { params });

      const apiResponse = response.data;

      if (apiResponse.status !== 'success') {
        throw new Error(`API Error: ${apiResponse.message}`);
      }

      if (import.meta.env.DEV) {
        console.log('=== API Response ===');
        console.log('Status:', apiResponse.status);
        console.log('Code:', apiResponse.code);
        console.log('Message:', apiResponse.message);
        console.log('Data length:', apiResponse.data?.length || 0);
        if (apiResponse.data?.length > 0) {
          console.log('First ticket sample:', apiResponse.data[0]);
        }
      }

      return apiResponse.data;
    } catch (error) {
      if (import.meta.env.DEV || import.meta.env.VITE_USE_MOCK_DATA === 'true') {
        console.error('Error fetching tickets for user:', userEmail, error);
        console.log('Falling back to mock data...');
        // 在开发环境下，如果API调用失败，回退到Mock数据
        return await this.getMockTicketsByUser(userEmail, from);
      }
      throw error;
    }
  }

  /**
   * 创建新票据
   * 后端API: POST /api/tickets
   */
  async createTicket(ticketData: Partial<Ticket>): Promise<Ticket> {
    try {
      if (import.meta.env.DEV) {
        console.log('Creating ticket:', ticketData);
      }

      // TODO: 替换为真实的API调用
      // const response = await request.post('/tickets', ticketData);
      // return response.data;

      // 目前使用Mock实现
      const newTicket: Ticket = {
        issues_id: `${Date.now()}`,
        issues_key: `TIC-${Math.floor(Math.random() * 10000)}`,
        summary: ticketData.summary || '',
        description: ticketData.description || '',
        status: 'Open',
        priority: ticketData.priority || 'Medium',
        service_package: ticketData.service_package || '',
        assignee_email: ticketData.assignee_email || '',
        reporter_email: ticketData.reporter_email || '',
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        component: ticketData.component || '',
        ordering_business_line: '',
        lab_location: '',
        issue_type: '',
        assignee: '',
        reporter: '',
        resolution: '',
        resolved: '',
        time_to_resolution: '',
        feedback: '',
        watchers: '',
        lsdsup_remaining_time: '',
        yes_sla_missed: 0,
        worklogs: [],
        catalog: '',
        vector_embedding: null,
        filtered_desc: '',
        ai_decision_logs: '',
        human_feedback_annotations: ''
      };

      return newTicket;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error creating ticket:', error);
      }
      throw error;
    }
  }

  /**
   * 更新票据
   * 后端API: PUT /api/tickets/:id
   */
  async updateTicket(ticketId: string, updates: Partial<Ticket>): Promise<Ticket> {
    try {
      if (import.meta.env.DEV) {
        console.log('Updating ticket:', ticketId, updates);
      }

      // TODO: 替换为真实的API调用
      // const response = await request.put(`/tickets/${ticketId}`, updates);
      // return response.data;

      throw new Error('Update ticket not implemented yet');
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error updating ticket:', ticketId, error);
      }
      throw error;
    }
  }

  /**
   * 根据ID获取票据
   * 后端API: GET /api/tickets/:id
   */
  async getTicketById(ticketId: string): Promise<Ticket> {
    try {
      if (import.meta.env.DEV) {
        console.log('Fetching ticket by ID:', ticketId);
      }

      // TODO: 替换为真实的API调用
      // const response = await request.get(`/tickets/${ticketId}`);
      // return response.data;

      throw new Error('Get ticket by ID not implemented yet');
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error fetching ticket by ID:', ticketId, error);
      }
      throw error;
    }
  }

  /**
   * 删除票据
   * 后端API: DELETE /api/tickets/:id
   */
  async deleteTicket(ticketId: string): Promise<void> {
    try {
      if (import.meta.env.DEV) {
        console.log('Deleting ticket:', ticketId);
      }

      // TODO: 替换为真实的API调用
      // await request.delete(`/tickets/${ticketId}`);

      throw new Error('Delete ticket not implemented yet');
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error deleting ticket:', ticketId, error);
      }
      throw error;
    }
  }

  /**
   * 为票据添加工作日志
   * 后端API: POST /api/tickets/:id/worklogs
   */
  async addWorklogToTicket(ticketId: string, worklogData: Partial<Worklog>): Promise<Ticket> {
    try {
      if (import.meta.env.DEV) {
        console.log('Adding worklog to ticket:', ticketId, worklogData);
      }

      // TODO: 替换为真实的API调用
      // const response = await request.post(`/tickets/${ticketId}/worklogs`, worklogData);
      // return response.data;

      // 目前使用Mock实现
      return await this.mockAddWorklogToTicket(ticketId, worklogData);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error('Error adding worklog to ticket:', ticketId, error);
      }
      throw error;
    }
  }

  /**
   * Mock数据实现 - 根据用户邮箱获取票据
   * 返回该用户相关的所有票据（创建的和分配的）
   */
  private async getMockTicketsByUser(userEmail: string, from: string): Promise<Ticket[]> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));

    // 注意：在Mock实现中，我们暂时忽略from参数，返回所有mock数据
    // 在真实API实现中，应该根据from参数过滤数据
    console.log('Mock API: from parameter received but ignored in mock implementation:', from);

    const createdTickets = generateMockTickets('created', userEmail);
    const assignedTickets = generateMockTickets('assigned', userEmail);

    // 返回合并的票据数组，前端会根据邮箱自动过滤
    return [...createdTickets, ...assignedTickets];
  }

  /**
   * Mock实现 - 为票据添加工作日志
   * 注意：这个方法只是模拟API调用，实际的数据更新由前端store处理
   */
  private async mockAddWorklogToTicket(ticketId: string, worklogData: Partial<Worklog>): Promise<Ticket> {
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 100));

    // 创建新的worklog记录
    const newWorklog: Worklog = {
      id: Date.now(), // 使用时间戳作为临时ID
      worklog_author: worklogData.worklog_author || 'Unknown',
      worklog_time: worklogData.worklog_time || new Date().toISOString(),
      worklog_timeSpent: worklogData.worklog_timeSpent || 0,
      worklog_comment: worklogData.worklog_comment || '',
      ticket_id: ticketId
    };

    // 创建一个模拟的更新后票据对象
    // 实际应用中，这里会从数据库返回完整的更新后票据
    const mockUpdatedTicket: Partial<Ticket> = {
      issues_id: ticketId,
      updated: new Date().toISOString(),
      worklogs: [newWorklog] // 这里只包含新添加的worklog，前端会合并到现有数据
    };

    if (import.meta.env.DEV) {
      console.log('Mock: Added worklog to ticket:', ticketId, newWorklog);
    }

    return mockUpdatedTicket as Ticket;
  }
}

/**
 * 生成Mock票据数据
 */
export function generateMockTickets(filter: string, userEmail: string = '<EMAIL>'): Ticket[] {
  const createdTickets: Ticket[] = [
    {
      issues_id: '101',
      issues_key: 'TIC-101',
      ordering_business_line: 'IT Support',
      lab_location: 'Beijing Office',
      issue_type: 'Network Issue',
      summary: 'Office network connection abnormal, unable to access internal servers',
      assignee: 'IT Support Team',
      reporter: 'Current User',
      priority: 'High',
      status: 'Open',
      resolution: '',
      created: '2025-07-18T09:30:00Z',
      resolved: '',
      updated: '2025-07-18T14:15:00Z',
      time_to_resolution: '',
      service_package: 'network-troubleshooting',
      feedback: '',
      component: 'Beijing Office - 3F Development Dept',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|Office 3F network connection unstable, frequent disconnections affecting daily work efficiency|',
      assignee_email: '<EMAIL>',
      reporter_email: userEmail,
      worklogs: [
        {
          id: 1,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-18T14:15:00Z',
          worklog_timeSpent: 2.5,
          worklog_comment: 'Troubleshooting network equipment failure, switch status checked',
          ticket_id: '101'
        }
      ],
      catalog: 'Network',
      vector_embedding: null,
      filtered_desc: 'Office network connection abnormal',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: 'created-2',
      issues_key: 'TIC-102',
      ordering_business_line: 'IT Support',
      lab_location: 'Beijing Office',
      issue_type: 'Software Installation',
      summary: 'Request to install latest version of Adobe Photoshop 2024',
      assignee: 'Software Admin',
      reporter: 'Current User',
      priority: 'Medium',
      status: 'In Progress',
      resolution: '',
      created: '2025-07-17T16:20:00Z',
      resolved: '',
      updated: '2025-07-18T10:30:00Z',
      time_to_resolution: '',
      service_package: 'software-installation',
      feedback: '',
      component: 'Beijing Office - 4F Design Dept',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Software Name||Version||Installation Reason||%0A|1|Adobe Photoshop|2024|Design department needs to use latest features for product design|',
      assignee_email: '<EMAIL>',
      reporter_email: userEmail,
      worklogs: [
        {
          id: 1,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-18T10:30:00Z',
          worklog_timeSpent: 1.0,
          worklog_comment: 'Installation package downloaded, software deployment in progress',
          ticket_id: 'created-2'
        }
      ],
      catalog: 'Software',
      vector_embedding: null,
      filtered_desc: 'Request to install Adobe Photoshop 2024',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: 'created-3',
      issues_key: 'TIC-103',
      ordering_business_line: 'Asset Management',
      lab_location: 'Multi-location',
      issue_type: 'Asset Transfer',
      summary: 'Laptop transfer from Beijing office to Shanghai office',
      assignee: 'Asset Manager',
      reporter: 'Current User',
      priority: 'Low',
      status: 'Resolved',
      resolution: 'Completed',
      created: '2025-07-16T14:00:00Z',
      resolved: '2025-07-17T17:45:00Z',
      updated: '2025-07-17T17:45:00Z',
      time_to_resolution: '1 day 3 hours',
      service_package: 'asset-transfer',
      feedback: 'Excellent service',
      component: 'Shanghai Office - 2F Sales Dept',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Sending Site||Receiving Site||Asset Number||Description||Serial Number||%0A|1|Beijing Office|Shanghai Office|NB202401|ThinkPad X1 Carbon|PC123456789|',
      assignee_email: '<EMAIL>',
      reporter_email: userEmail,
      worklogs: [
        {
          id: 1,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-16T14:30:00Z',
          worklog_timeSpent: 1.0,
          worklog_comment: 'Initial assessment and preparation for equipment transfer',
          ticket_id: 'created-3'
        },
        {
          id: 2,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-17T10:00:00Z',
          worklog_timeSpent: 2.0,
          worklog_comment: 'Equipment packaging and transportation to Shanghai office',
          ticket_id: 'created-3'
        },
        {
          id: 3,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-17T17:45:00Z',
          worklog_timeSpent: 1.5,
          worklog_comment: 'Equipment successfully transferred and accepted, user confirmed device is in good condition',
          ticket_id: 'created-3'
        }
      ],
      catalog: 'Asset',
      vector_embedding: null,
      filtered_desc: 'Laptop transfer',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: 'created-4',
      issues_key: 'TIC-104',
      ordering_business_line: 'Facility Management',
      lab_location: 'Beijing Office',
      issue_type: 'Hardware Issue',
      summary: 'Conference room projector cannot display properly, screen showing distorted images',
      assignee: 'Facility Support',
      reporter: 'Current User',
      priority: 'Critical',
      status: 'Open',
      resolution: '',
      created: '2025-07-17T11:45:00Z',
      resolved: '',
      updated: '2025-07-17T13:20:00Z',
      time_to_resolution: '',
      service_package: 'network-troubleshooting',
      feedback: '',
      component: 'Beijing Office - 5F Conference Room',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|Projector in Building A 5F main conference room showing distorted display, unable to conduct meeting presentations normally|',
      assignee_email: '<EMAIL>',
      reporter_email: userEmail,
      worklogs: [
        {
          id: 1,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-17T13:20:00Z',
          worklog_timeSpent: 0.5,
          worklog_comment: 'On-site inspection completed, preliminary assessment indicates projector bulb or motherboard issue',
          ticket_id: 'created-4'
        }
      ],
      catalog: 'Hardware',
      vector_embedding: null,
      filtered_desc: 'Conference room projector malfunction',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: 'created-5',
      issues_key: 'TIC-105',
      ordering_business_line: 'IT Support',
      lab_location: 'Beijing Office',
      issue_type: 'Software Installation',
      summary: 'Request to install Microsoft Office 365 Enterprise',
      assignee: 'Software Admin',
      reporter: 'Current User',
      priority: 'Medium',
      status: 'Closed',
      resolution: 'Completed',
      created: '2025-07-15T10:15:00Z',
      resolved: '2025-07-16T15:30:00Z',
      updated: '2025-07-16T15:30:00Z',
      time_to_resolution: '1 day 5 hours',
      service_package: 'software-installation',
      feedback: 'Good service',
      component: 'Beijing Office - 3F Development Dept',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Software Name||Version||Installation Reason||%0A|1|Microsoft Office 365|Enterprise|New employee onboarding requires complete office software suite|',
      assignee_email: '<EMAIL>',
      reporter_email: userEmail,
      worklogs: [
        {
          id: 1,
          worklog_author: '<EMAIL>',
          worklog_time: '2025-07-16T15:30:00Z',
          worklog_timeSpent: 2.0,
          worklog_comment: 'Software installation completed, enterprise email and OneDrive sync configured',
          ticket_id: 'created-5'
        }
      ],
      catalog: 'Software',
      vector_embedding: null,
      filtered_desc: 'Request to install Office 365',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    }
  ];

  const assignedTickets: Ticket[] = [
    // =================================================================
    // 5 Unfinished Tickets
    // =================================================================
    {
      issues_id: '201',
      issues_key: 'TIC-201',
      ordering_business_line: 'IT Support',
      lab_location: 'Data Center',
      issue_type: 'Network Issue',
      summary: 'Employee feedback on email server connection timeout issue',
      assignee: 'Current User',
      reporter: 'HR Manager',
      priority: 'High',
      status: 'In Progress',
      resolution: '',
      created: '2025-07-15T08:00:00Z',
      resolved: '',
      updated: '2025-07-17T10:00:00Z',
      time_to_resolution: '',
      service_package: 'network-troubleshooting',
      feedback: '',
      component: 'Data Center - Zone A Server Room',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|Multiple employees report inability to connect to email server, showing connection timeout error|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-15T09:30:00Z', worklog_timeSpent: 1.5, worklog_comment: 'Initial investigation. Confirmed issue is widespread.', ticket_id: '201' },
        { id: 2, worklog_author: '<EMAIL>', worklog_time: '2025-07-16T11:00:00Z', worklog_timeSpent: 2.0, worklog_comment: 'Checked firewall rules, found a recent change that might be the cause. Reverting change for testing.', ticket_id: '201' },
        { id: 3, worklog_author: userEmail, worklog_time: '2025-07-17T10:00:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Firewall change reverted. Monitoring system and waiting for user feedback.', ticket_id: '201' }
      ],
      catalog: 'Network',
      vector_embedding: null,
      filtered_desc: 'Email server connection timeout',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '206',
      issues_key: 'TIC-206',
      ordering_business_line: 'Account Management',
      lab_location: 'Shanghai R&D',
      issue_type: 'Account Access',
      summary: 'Cannot access internal Git repository',
      assignee: 'Current User',
      reporter: 'Software Engineer',
      priority: 'High',
      status: 'In Progress',
      resolution: '',
      created: '2025-07-16T10:00:00Z',
      resolved: '',
      updated: '2025-07-17T11:30:00Z',
      time_to_resolution: '',
      service_package: 'account-services',
      feedback: '',
      component: 'DevOps Tools',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|I am getting a 403 Forbidden error when trying to clone a repository from our internal GitLab.|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-16T10:30:00Z', worklog_timeSpent: 0.5, worklog_comment: 'Checked user permissions in GitLab. User is part of the correct group.', ticket_id: '206' },
        { id: 2, worklog_author: '<EMAIL>', worklog_time: '2025-07-16T15:00:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Investigating potential VPN or network policy issues. The user is connecting from a new location.', ticket_id: '206' },
        { id: 3, worklog_author: userEmail, worklog_time: '2025-07-17T11:30:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Confirmed with network team that the new IP range needs to be whitelisted. Submitted a request.', ticket_id: '206' }
      ],
      catalog: 'Account',
      vector_embedding: null,
      filtered_desc: 'Git repository access denied',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '208',
      issues_key: 'TIC-208',
      ordering_business_line: 'IT Support',
      lab_location: 'Shenzhen Office',
      issue_type: 'Hardware Issue',
      summary: 'Laptop screen is flickering',
      assignee: 'Current User',
      reporter: 'Marketing Specialist',
      priority: 'High',
      status: 'In Progress',
      resolution: '',
      created: '2025-07-17T09:05:00Z',
      resolved: '',
      updated: '2025-07-17T11:45:00Z',
      time_to_resolution: '',
      service_package: 'hardware-repair',
      feedback: '',
      component: 'Employee Hardware',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|My laptop screen started flickering this morning. It gets worse when I move the lid.|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-17T09:30:00Z', worklog_timeSpent: 0.5, worklog_comment: 'Contacted user. Ran remote diagnostics. Suspected loose display cable or failing GPU.', ticket_id: '208' },
        { id: 2, worklog_author: userEmail, worklog_time: '2025-07-17T11:45:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Arranged for a temporary replacement laptop for the user. The faulty laptop has been sent for physical inspection.', ticket_id: '208' }
      ],
      catalog: 'Hardware',
      vector_embedding: null,
      filtered_desc: 'Laptop screen flickering',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
     {
      issues_id: '212',
      issues_key: 'TIC-212',
      ordering_business_line: 'IT Support',
      lab_location: 'Remote',
      issue_type: 'Network Issue',
      summary: 'VPN connection keeps dropping every 15-20 minutes',
      assignee: 'Current User',
      reporter: 'Sales Director',
      priority: 'High',
      status: 'Open',
      resolution: '',
      created: '2025-07-17T11:00:00Z',
      resolved: '',
      updated: '2025-07-17T12:00:00Z',
      time_to_resolution: '',
      service_package: 'network-troubleshooting',
      feedback: '',
      component: 'VPN Service',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|My VPN connection is unstable today, it disconnects frequently making it impossible to work.|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-17T11:15:00Z', worklog_timeSpent: 0.5, worklog_comment: 'Initial contact with user. Asked for VPN logs and to run a speed test on their local connection.', ticket_id: '212' },
        { id: 2, worklog_author: userEmail, worklog_time: '2025-07-17T12:00:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Reviewing VPN server logs for user session data. No anomalies found on server side yet. Suspecting client-side issue.', ticket_id: '212' }
      ],
      catalog: 'Network',
      vector_embedding: null,
      filtered_desc: 'VPN connection dropping',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '214',
      issues_key: 'TIC-214',
      ordering_business_line: 'Infrastructure',
      lab_location: 'Data Center',
      issue_type: 'Performance Degradation',
      summary: 'High CPU usage on database server DB-02',
      assignee: 'Current User',
      reporter: 'Monitoring System',
      priority: 'Critical',
      status: 'In Progress',
      resolution: '',
      created: '2025-07-17T13:00:00Z',
      resolved: '',
      updated: '2025-07-17T14:00:00Z',
      time_to_resolution: '',
      service_package: 'system-maintenance',
      feedback: '',
      component: 'Database Servers',
      watchers: '<EMAIL>',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Alert Details||%0A|1|CPU utilization on server DB-02 has been over 95% for the last 30 minutes.|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: '<EMAIL>', worklog_time: '2025-07-17T13:05:00Z', worklog_timeSpent: 0.5, worklog_comment: 'Alert acknowledged. Assigning to team for immediate investigation.', ticket_id: '214' },
        { id: 2, worklog_author: userEmail, worklog_time: '2025-07-17T14:00:00Z', worklog_timeSpent: 1.0, worklog_comment: 'Logged into DB-02. Identified a runaway query from the reporting service. Working on killing the process and analyzing the query.', ticket_id: '214' }
      ],
      catalog: 'Infrastructure',
      vector_embedding: null,
      filtered_desc: 'High CPU on database server',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    // =================================================================
    // 5 Finished Tickets
    // =================================================================
    {
      issues_id: 'assigned-3',
      issues_key: 'TIC-203',
      ordering_business_line: 'Infrastructure',
      lab_location: 'Data Center',
      issue_type: 'Asset Transfer',
      summary: 'Server room equipment migration from old server room to new server room',
      assignee: 'Current User',
      reporter: 'Datacenter Manager',
      priority: 'Critical',
      status: 'Resolved',
      resolution: 'Completed',
      created: '2025-06-25T09:00:00Z',
      resolved: '2025-06-26T18:00:00Z',
      updated: '2025-06-26T18:00:00Z',
      time_to_resolution: '1 day 9 hours',
      service_package: 'asset-transfer',
      feedback: 'Excellent work',
      component: 'Data Center - Zone B Server Room',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Sending Site||Receiving Site||Asset Number||Description||Serial Number||%0A|1|Old Server Room Zone A|New Server Room Zone B|SV202401|Dell PowerEdge R740|SV987654321|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-06-25T09:30:00Z', worklog_timeSpent: 2.0, worklog_comment: 'Initial assessment and migration planning, equipment inventory check',ticket_id: 'assigned-3' },
        { id: 2, worklog_author: userEmail, worklog_time: '2025-06-25T14:00:00Z', worklog_timeSpent: 4.0, worklog_comment: 'Server shutdown and physical migration to new location' ,ticket_id: 'assigned-3'},
        { id: 3, worklog_author: userEmail, worklog_time: '2025-06-26T18:00:00Z', worklog_timeSpent: 2.0, worklog_comment: 'Server migration completed, all services restored to normal operation, performance tests passed',ticket_id: 'assigned-3' }
      ],
      catalog: 'Infrastructure',
      vector_embedding: null,
      filtered_desc: 'Server room equipment migration',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: 'assigned-5',
      issues_key: 'TIC-205',
      ordering_business_line: 'IT Support',
      lab_location: 'Shenzhen Office',
      issue_type: 'Software Installation',
      summary: 'Install professional design software AutoCAD 2024',
      assignee: 'Current User',
      reporter: 'Engineering Manager',
      priority: 'Medium',
      status: 'Closed',
      resolution: 'Completed',
      created: '2025-07-01T11:20:00Z',
      resolved: '2025-07-02T16:45:00Z',
      updated: '2025-07-02T16:45:00Z',
      time_to_resolution: '1 day 5 hours',
      service_package: 'software-installation',
      feedback: 'Very satisfied',
      component: 'Shenzhen Office - 2F Technical Dept',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Software Name||Version||Installation Reason||%0A|1|AutoCAD|2024|Engineering department needs to perform architectural design and drafting work|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-02T16:45:00Z', worklog_timeSpent: 3.0, worklog_comment: 'AutoCAD 2024 installation completed, license and user permissions configured, user training completed', ticket_id: 'assigned-5' }
      ],
      catalog: 'Software',
      vector_embedding: null,
      filtered_desc: 'Install AutoCAD 2024',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '215',
      issues_key: 'TIC-215',
      ordering_business_line: 'IT Support',
      lab_location: 'Beijing Office',
      issue_type: 'Account Access',
      summary: 'User forgot password for Windows account',
      assignee: 'Current User',
      reporter: 'Analyst',
      priority: 'Medium',
      status: 'Resolved',
      resolution: 'Done',
      created: '2025-07-14T09:10:00Z',
      resolved: '2025-07-14T09:25:00Z',
      updated: '2025-07-14T09:25:00Z',
      time_to_resolution: '15 minutes',
      service_package: 'account-services',
      feedback: 'Fast response, thanks!',
      component: 'Active Directory',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|User is locked out of their workstation and needs a password reset.|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-14T09:25:00Z', worklog_timeSpent: 0.25, worklog_comment: 'Verified user identity via phone call. Reset password and provided temporary password. User confirmed they can log in.', ticket_id: '215' }
      ],
      catalog: 'Account',
      vector_embedding: null,
      filtered_desc: 'Windows password reset',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '217',
      issues_key: 'TIC-217',
      ordering_business_line: 'IT Support',
      lab_location: 'Shenzhen Office',
      issue_type: 'Software Issue',
      summary: 'Microsoft Office needs to be reactivated',
      assignee: 'Current User',
      reporter: 'Sales Associate',
      priority: 'Low',
      status: 'Closed',
      resolution: 'Fixed',
      created: '2025-07-08T10:12:00Z',
      resolved: '2025-07-08T10:45:00Z',
      updated: '2025-07-08T10:45:00Z',
      time_to_resolution: '33 minutes',
      service_package: 'application-support',
      feedback: 'Quick and easy fix!',
      component: 'Microsoft Office Suite',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||Issue Description||%0A|1|My Word and Excel are showing an "Unlicensed Product" error and asking for activation.',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-08T10:45:00Z', worklog_timeSpent: 0.25, worklog_comment: 'Ran the office activation script remotely. User confirmed the issue is resolved.', ticket_id: '217' }
      ],
      catalog: 'Software',
      vector_embedding: null,
      filtered_desc: 'Office reactivation required',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    },
    {
      issues_id: '220',
      issues_key: 'TIC-220',
      ordering_business_line: 'Account Management',
      lab_location: 'Shanghai R&D',
      issue_type: 'Account Access',
      summary: 'Access required for new team member to shared Confluence space',
      assignee: 'Current User',
      reporter: 'Team Lead',
      priority: 'Low',
      status: 'Resolved',
      resolution: 'Done',
      created: '2025-07-10T16:00:00Z',
      resolved: '2025-07-10T16:30:00Z',
      updated: '2025-07-10T16:30:00Z',
      time_to_resolution: '30 minutes',
      service_package: 'account-services',
      feedback: 'Very fast!',
      component: 'Confluence',
      watchers: '',
      lsdsup_remaining_time: '',
      yes_sla_missed: 0,
      description: '||NO.||User||Space||Permission Level||%0A|1|<EMAIL>|Project Phoenix|Read/Write|',
      assignee_email: userEmail,
      reporter_email: '<EMAIL>',
      worklogs: [
        { id: 1, worklog_author: userEmail, worklog_time: '2025-07-10T16:30:00Z', worklog_timeSpent: 0.5, worklog_comment: 'Permissions granted to the user for the specified Confluence space. Confirmed with reporter.', ticket_id: '220' }
      ],
      catalog: 'Account',
      vector_embedding: null,
      filtered_desc: 'Confluence space access',
      ai_decision_logs: '',
      human_feedback_annotations: ''
    }
  ];

  return filter === 'created' ? createdTickets : assignedTickets;
}

// 导出API实例
export const ticketAPI = new TicketAPIImpl();
